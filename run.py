"""
抖音批量私信工具启动器
支持命令行和GUI两种模式
"""

import sys
import argparse


def run_cli():
    """运行命令行版本"""
    print("启动命令行版本...")
    from main import main
    main()


def run_gui():
    """运行GUI版本"""
    print("启动GUI版本...")
    try:
        from ui_main import main
        main()
    except ImportError as e:
        print(f"❌ GUI依赖缺失: {e}")
        print("请安装GUI依赖: pip install PyQt6 qdarktheme")
        print("或使用命令行版本: python run.py --cli")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="抖音批量私信工具")
    parser.add_argument("--cli", action="store_true", help="使用命令行模式")
    parser.add_argument("--gui", action="store_true", help="使用GUI模式")
    
    args = parser.parse_args()
    
    if args.cli:
        run_cli()
    elif args.gui:
        run_gui()
    else:
        # 默认尝试启动GUI，失败则提示
        print("🎯 抖音批量私信工具")
        print("=" * 30)
        print("1. GUI模式 (推荐)")
        print("2. 命令行模式")
        print()
        
        choice = input("请选择模式 (1/2) [默认: 1]: ").strip()
        
        if choice == "2":
            run_cli()
        else:
            run_gui()


if __name__ == "__main__":
    main()
