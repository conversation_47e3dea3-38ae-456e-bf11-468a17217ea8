# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 DrissionPage 的抖音自动化脚本研究项目，用于教育和研究目的。项目演示了web自动化的基本机制，帮助理解和防范潜在的自动化滥用行为。

## 核心依赖

- **DrissionPage**: 用于浏览器自动化操作的Python库
- **time**: 标准库，用于延时控制

## 运行环境

- **系统**: Windows
- **终端**: PowerShell 7
- **Python环境**: Anaconda3 base环境
- **浏览器**: Chrome/Chromium

## 常用命令

```powershell
# 运行主程序
python main.py

# 安装依赖（如果需要）
pip install DrissionPage
```

## 代码架构

项目采用单文件结构，包含以下核心组件：

### 主要函数模块

- `find_dm_ele_click()`: 定位并点击私信按钮，使用JavaScript点击避免位置问题
- `find_dm_input_click()`: 定位输入框并发送消息，支持自动回车
- `Chrom_sets()`: 浏览器配置设置，默认非无头模式便于调试
- `base_sets()`: 基础参数配置，包含sec_uids列表和批量处理配置
- `main()`: 主执行流程，实现批量处理逻辑和错误隔离

### 批量处理架构

项目支持批量处理多个sec_uid，核心特性：
- **错误隔离**: 单个用户失败不影响其他用户处理
- **进度跟踪**: 实时显示处理进度(当前/总数)
- **自动间隔**: 用户间自动等待2秒，避免操作过快
- **统计汇总**: 处理完成后显示成功/失败统计

### 执行流程

1. **初始化阶段**: 从base_sets()获取配置参数和sec_uids列表
2. **浏览器启动**: 通过Chrom_sets()创建ChromiumPage实例
3. **批量处理循环**: 遍历sec_uids列表，对每个用户执行：
   - 导航到用户主页
   - 定位并点击私信按钮
   - 定位输入框并发送消息
   - 异常处理和进度记录
4. **结果汇总**: 显示处理统计信息

## 关键实现细节

- **元素定位策略**: 使用DrissionPage的语义化选择器，如`'tag:span@@class=semi-button-content@@text()=私信'`
- **点击机制**: 优先使用`click(by_js=True)`避免页面布局导致的点击失效
- **消息格式**: 自动在消息末尾添加换行符(`\n`)
- **浏览器实例管理**: 注释了`page.close()`以便手动检查最终状态

## 配置说明

关键配置参数在 `base_sets()` 函数中：

- `time_out`: 页面加载等待时间（默认5秒）
- `send_interval`: 消息发送间隔（默认1秒）
- `sec_uids`: 目标用户ID列表，支持20个用户的批量处理
- `message`: 要发送的消息内容（默认"你好！"）

## 安全注意事项

- 项目仅用于教育研究目的
- 实际使用时需遵守平台服务条款
- 建议在测试环境中运行
- 避免频繁自动化操作以防被检测