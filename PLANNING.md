# 项目规划文档

## 项目概述

抖音自动化私信工具 - 基于DrissionPage的web自动化脚本，用于教育和研究目的。

## 项目目标

- **主要目标**: 实现抖音用户私信自动化发送功能
- **核心功能**: 支持批量处理多个用户
- **教育目标**: 演示web自动化机制，帮助理解和防范自动化滥用

## 技术架构

### 核心技术栈
- **DrissionPage**: 浏览器自动化操作
- **Python 3.x**: 主要开发语言
- **Chrome/Chromium**: 目标浏览器

### 代码架构原则
- **简单务实**: 最小化复杂性，直接解决问题
- **单一职责**: 每个函数专注单一功能
- **错误隔离**: 单个操作失败不影响整体流程

### 文件组织
- `main.py`: 主程序，包含所有核心逻辑
- `README.md`: 使用说明文档
- `CLAUDE.md`: 项目技术说明

## 编码规范

### 代码风格
- 保持简单直接的实现方式
- 清晰的变量命名
- 适当的注释说明

### 安全约束
- 仅用于教育研究目的
- 遵守平台服务条款
- 避免频繁操作防止被检测
- 在测试环境中运行

## 功能特性

### 已实现功能
- 批量处理多个sec_uid
- 错误隔离机制
- 进度跟踪显示
- 自动处理间隔
- 浏览器自动化控制

### 扩展方向
- GUI界面集成
- 配置文件管理
- 更丰富的错误处理
- 日志记录功能
