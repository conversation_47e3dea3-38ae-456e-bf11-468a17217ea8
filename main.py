from DrissionPage import ChromiumPage,ChromiumOptions
import time

def find_dm_ele_click(time_out,page):
    time.sleep(time_out)

    # 查找私信按钮
    dm_ele = page.ele('tag:span@@class=semi-button-content@@text()=私信')

    if dm_ele:
        print(f"找到私信按钮: {dm_ele.text}")
        # 使用JavaScript点击，避免位置问题
        dm_ele.click(by_js=True)
        time.sleep(1)
        dm_ele.click(by_js=True)
        print("私信按钮点击成功！")
        return True
    else:
        print("未找到私信按钮")
        return False

def find_dm_input_click(time_out,page,message,send_interval):
    time.sleep(time_out)
    # 查找发送消息输入框
    dm_input = page.ele('text:发送消息')
    if dm_input:
        print(f"找到发送消息输入框: {dm_input.text}")
        # 使用JavaScript点击，避免位置问题
        dm_input.click(by_js=True)
        time.sleep(1)
        dm_input.click(by_js=True)
        print("发送消息输入框点击成功！")
        #输入消息
        dm_input.input(message)
        time.sleep(send_interval)
        return True
    else:
        print("未找到发送消息输入框")
        return False

def Chrom_sets():
    #设置浏览器选项
    co = ChromiumOptions()
    #设置无图模式
    # co.no_imgs(True)
    #设置无头模式
    co.headless(False)
    page = ChromiumPage(co)
    return page

def base_sets():
    #设置打开主页延迟
    time_out = 5
    #发送间隔
    send_interval = 1
    #设置sec_uid列表 - 支持多个用户
    sec_uids = [
        'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x',
        'MS4wLjABAAAAGvL6Bj9amwUVgUSwj7gBeV8Ylw8w-lFi1eqtA23MqopcQKjUHkCPsbAzFKvZKw62',
        'MS4wLjABAAAAZC6T_MEEHdjauTZIrgj5I7hthsOS8R-9yjvntxbDJJE',
        'MS4wLjABAAAA5k8t7V6cxKEs52fxdAA0pd2py6BM_pdijNzt7lgRNvDJ1euoDIIIjEJwvqfHHtom',
        'MS4wLjABAAAANmzXd9AH3tF-MhxnAvIThJ2SxgUmwa56rIVrbkwbXcAUSdhUVmRcS2gTaAdV0Sl5',
        'MS4wLjABAAAAVpVGxT99ZzGmtxIylP6CtT_yJlu0tGV2nmvEcncpq3U',
        'MS4wLjABAAAAcWn3liRw0lMe1X46mMnUduZjXIZE1OkrsleurwXNT2A',
        'MS4wLjABAAAAD6MgRI73kNCzVrlnlQQ5RQAppxcq26Fe_tujBFfpl8XOXf7eqg1BT177UiIk4SD1',
        'MS4wLjABAAAAT28ILUptSibG3aGT50eZBTqXtsmJ893VsybMbGaa3i92vXC_-UZO4XMsOdZRgIgs',
        'MS4wLjABAAAA60aTd74vtW_nVHCNyq0_JiDpCi4keBBof75h_SvYIfU',
        'MS4wLjABAAAApwZI9L1WDghEoFH-Ul69eHFak0quNWO_6dCApvKgmuwUu-HpNhBkolA7L2i5idwR',
        'MS4wLjABAAAAtP0dbmc9fk9JTuOlxEmubHhozXdHpuZYXfG_FlqOkNet4l4nC_ZpLwoRdznxjnJc',
        'MS4wLjABAAAAXSVXWYLJgkexWtzMpW6ZVvfEtZ5HJ7W-wC7ZR8Dw0T8',
        'MS4wLjABAAAAnagUEN-YnYdR76JGwAhmDIKvmf9Tq0CWAGBtI4GLE8sc6M5HoYLGU9ARmKZkAzvA',
        'MS4wLjABAAAAUL042e2Rq9D2JntHML8--Pi3WXL6kH-rzqziSYKahfevmNsENhGr8rMtDiJNCOa6',
        'MS4wLjABAAAAdxEVXrah6aTLsWTN4cKcBiPDUfSxndARtWnLffG0QPsB5r1uwr2SKsGajj6IqEuf',
        'MS4wLjABAAAAhX3Qy3-juAk2jRoEclkXgPYD7MwS7FnWTm7kdum7Gfc',
        'MS4wLjABAAAAeUA7TIGwi3vnxVjwZGatdEKbReznDCabonOayefagoIwQ4ZuwaFqkCtKqpYWqm3g',
        'MS4wLjABAAAATZah8OWTtnMhI3XJNlDmtai93ZnZsay7TWvpBFE6ydM',
        'MS4wLjABAAAAQ6obAcZio2nENNNykRx9qOpJNDc1u-9WQxcjSowMgcs'
    ]
    #要私信的消息
    message = "你好！"
    #给消息加上回车
    message += '\n'
    return time_out,send_interval,sec_uids,message


def main():
    #基础设置
    time_out,send_interval,sec_uids,message = base_sets()

    #浏览器设置
    page = Chrom_sets()

    # 批量处理每个sec_uid
    total = len(sec_uids)
    success_count = 0

    for i, sec_uid in enumerate(sec_uids, 1):
        print(f"\n=== 处理第 {i}/{total} 个用户: {sec_uid} ===")

        try:
            zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
            page.get(zhuye_url)
            print(page.title)

            # 等待页面加载
            dm_ele_click = find_dm_ele_click(time_out,page)
            if not dm_ele_click:
                print(f"用户 {sec_uid} 处理失败：未找到私信按钮")
                continue
            time.sleep(time_out)

            dm_input_click = find_dm_input_click(time_out,page,message,send_interval)
            if not dm_input_click:
                print(f"用户 {sec_uid} 处理失败：消息发送失败")
                continue

            success_count += 1
            print(f"✓ 用户 {sec_uid} 处理成功")

        except Exception as e:
            print(f"✗ 用户 {sec_uid} 处理出错: {e}")

        # 处理间隔，避免操作过快
        if i < total:
            print("等待2秒后处理下一个用户...")
            time.sleep(2)

    print(f"\n=== 批量处理完成 ===")
    print(f"总计: {total} 个用户，成功: {success_count} 个，失败: {total - success_count} 个")

    page.close()

if __name__ == '__main__':
    main()