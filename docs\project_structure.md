# 项目结构说明

## 文件组织

```
douyin_dm/
├── main.py                 # 核心批量处理逻辑（命令行版本）
├── ui_main.py             # Qt6 GUI界面
├── run.py                 # 启动器（选择CLI或GUI模式）
├── install.py             # 依赖安装脚本
├── requirements.txt       # 项目依赖列表
├── README.md             # 使用说明文档
├── PLANNING.md           # 项目规划文档
├── TASK.md               # 任务管理文档
├── CLAUDE.md             # 技术说明文档
├── docs/                 # 文档目录
│   ├── project_structure.md
│   └── batch_architecture.md
├── test/                 # 测试目录
│   └── test_batch_processing.py
└── __pycache__/          # Python缓存文件
```

## 核心文件说明

### main.py
- **功能**: 批量处理核心逻辑
- **特性**: 
  - 支持多个sec_uid批量处理
  - 错误隔离机制
  - 进度显示
  - 自动处理间隔

### ui_main.py
- **功能**: Qt6图形界面
- **特性**:
  - 现代化暗色主题界面
  - 实时进度显示
  - 多线程处理
  - 日志输出和保存
  - 配置参数调整

### run.py
- **功能**: 统一启动器
- **用法**:
  ```bash
  python run.py          # 交互式选择模式
  python run.py --gui     # 直接启动GUI
  python run.py --cli     # 直接启动命令行
  ```

### install.py
- **功能**: 自动安装依赖
- **特性**:
  - 检查已安装包
  - 选择性安装GUI依赖
  - 友好的安装提示

## 使用流程

### 首次使用
1. 运行 `python install.py` 安装依赖
2. 运行 `python run.py` 选择使用模式

### GUI模式
1. 启动: `python ui_main.py` 或 `python run.py --gui`
2. 配置消息内容和参数
3. 输入SEC_UID列表
4. 点击开始处理
5. 查看实时进度和日志

### 命令行模式
1. 启动: `python main.py` 或 `python run.py --cli`
2. 修改 `main.py` 中的 `base_sets()` 函数配置
3. 运行程序查看处理结果

## 技术架构

### 核心组件
- **浏览器控制**: DrissionPage
- **GUI框架**: PyQt6
- **多线程**: QThread
- **样式主题**: 自定义CSS暗色主题

### 数据流
1. 用户输入 → 配置验证
2. 创建工作线程 → 批量处理
3. 实时进度更新 → 界面反馈
4. 处理完成 → 结果统计

### 错误处理
- 单个用户失败不影响整体处理
- 异常捕获和日志记录
- 用户友好的错误提示

## 扩展方向

### 已实现功能
- ✅ 批量处理多个用户
- ✅ GUI和命令行双模式
- ✅ 实时进度跟踪
- ✅ 错误隔离机制
- ✅ 日志记录和保存

### 计划功能
- 🔄 配置文件管理
- 🔄 用户数据导入/导出
- 🔄 更完善的错误重试
- 🔄 处理历史记录
- 🔄 多账号支持
