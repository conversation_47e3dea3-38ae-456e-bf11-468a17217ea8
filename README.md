# 抖音批量私信工具

## 功能说明

这是一个基于DrissionPage的抖音自动化私信工具，支持批量向多个用户发送私信。

## 主要特性

- ✅ **批量处理**: 支持一次性向多个用户发送私信
- ✅ **错误隔离**: 单个用户处理失败不影响其他用户
- ✅ **进度显示**: 实时显示处理进度和结果
- ✅ **自动间隔**: 用户间自动等待，避免操作过快
- ✅ **简单配置**: 只需修改sec_uid列表即可

## 使用方法

### 1. 安装依赖

**基础依赖（命令行版本）:**
```bash
pip install DrissionPage
```

**完整依赖（包含GUI）:**
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install DrissionPage PyQt6 qdarktheme
```

### 2. 启动程序

**方式一：使用启动器（推荐）**
```bash
python run.py
```
程序会提示选择GUI模式或命令行模式。

**方式二：直接启动GUI版本**
```bash
python ui_main.py
```

**方式三：直接启动命令行版本**
```bash
python main.py
```

### 3. GUI版本使用说明

1. **配置消息内容**: 在"消息内容"框中输入要发送的消息
2. **设置参数**: 调整页面等待时间和发送间隔
3. **输入用户列表**: 在"目标用户列表"中输入SEC_UID，每行一个
4. **开始处理**: 点击"🚀 开始处理"按钮
5. **查看进度**: 实时查看处理进度和日志
6. **保存日志**: 可以保存处理日志到文件

### 4. 命令行版本配置
编辑 `main.py` 文件中的 `base_sets()` 函数，在 `sec_uids` 列表中添加目标用户的sec_uid：

```python
sec_uids = [
    'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x',
    'MS4wLjABAAAAGvL6Bj9amwUVgUSwj7gBeV8Ylw8w-lFi1eqtA23MqopcQKjUHkCPsbAzFKvZKw62',
    # 添加更多sec_uid...
]
```

### 3. 自定义消息
在 `base_sets()` 函数中修改要发送的消息：

```python
message = "你好！"  # 修改为你想发送的消息
```

### 5. 运行程序
```bash
python main.py  # 命令行版本
```

## 运行效果

程序会依次处理每个用户，显示类似以下的输出：

```
=== 处理第 1/20 个用户: MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x ===
找到私信按钮: 私信
私信按钮点击成功！
找到发送消息输入框: 发送消息
发送消息输入框点击成功！
✓ 用户 MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x 处理成功
等待2秒后处理下一个用户...

=== 批量处理完成 ===
总计: 20 个用户，成功: 18 个，失败: 2 个
```

## 配置参数

在 `base_sets()` 函数中可以调整以下参数：

- `time_out`: 页面加载等待时间（默认5秒）
- `send_interval`: 消息发送间隔（默认1秒）
- `message`: 要发送的消息内容
- `sec_uids`: 目标用户ID列表

## 注意事项

- 仅用于教育研究目的
- 请遵守平台服务条款
- 建议在测试环境中运行
- 避免频繁操作以防被检测
- 程序会在用户间自动等待2秒，避免操作过快

## 获取sec_uid

sec_uid可以从抖音用户主页URL中获取，格式如：
`https://www.douyin.com/user/[SEC_UID]?from_tab_name=main`
