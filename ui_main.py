"""
抖音批量私信工具 - Qt6 GUI界面
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTextEdit, QPushButton, QLabel, 
                             QProgressBar, QGroupBox, QLineEdit, QSpinBox,
                             QTabWidget, QScrollArea, QFrame, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor

# 导入主程序逻辑
import main


class WorkerThread(QThread):
    """工作线程 - 执行批量处理任务"""
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 当前处理的用户
    result_ready = pyqtSignal(int, int)  # 成功数, 失败数
    log_message = pyqtSignal(str)

    def __init__(self, sec_uids, message, time_out, send_interval):
        super().__init__()
        self.sec_uids = sec_uids
        self.message = message
        self.time_out = time_out
        self.send_interval = send_interval
        self.should_stop = False

    def stop(self):
        """停止处理"""
        self.should_stop = True

    def run(self):
        """执行批量处理"""
        try:
            # 创建浏览器实例
            page = main.Chrom_sets()

            total = len(self.sec_uids)
            success_count = 0
            failed_count = 0

            for i, sec_uid in enumerate(self.sec_uids):
                if self.should_stop:
                    break

                self.log_message.emit(f"正在处理第 {i+1}/{total} 个用户: {sec_uid}")
                self.progress_updated.emit(int((i / total) * 100), sec_uid)

                try:
                    # 导航到用户页面
                    zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
                    page.get(zhuye_url)

                    # 点击私信按钮
                    dm_ele_success = main.find_dm_ele_click(self.time_out, page)
                    if not dm_ele_success:
                        failed_count += 1
                        self.log_message.emit(f"❌ 用户 {sec_uid} 处理失败：未找到私信按钮")
                        continue

                    # 发送消息
                    dm_input_success = main.find_dm_input_click(self.time_out, page, self.message, self.send_interval)
                    if not dm_input_success:
                        failed_count += 1
                        self.log_message.emit(f"❌ 用户 {sec_uid} 处理失败：消息发送失败")
                        continue

                    success_count += 1
                    self.log_message.emit(f"✅ 用户 {sec_uid} 处理成功")

                except Exception as e:
                    failed_count += 1
                    self.log_message.emit(f"❌ 用户 {sec_uid} 处理出错: {str(e)}")

                # 处理间隔
                if i < total - 1:
                    import time
                    time.sleep(2)

            # 关闭浏览器
            try:
                page.close()
            except:
                pass

            self.progress_updated.emit(100, "")
            self.result_ready.emit(success_count, failed_count)

        except Exception as e:
            self.log_message.emit(f"处理过程中发生严重错误: {str(e)}")


class DouYinBatchUI(QMainWindow):
    """抖音批量私信工具主界面"""
    
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("抖音批量私信工具 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧日志面板
        right_panel = self.create_log_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setSizes([400, 800])
        
    def create_control_panel(self):
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标题
        title_label = QLabel("🎯 抖音批量私信工具")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 配置区域
        config_group = QGroupBox("⚙️ 基础配置")
        config_layout = QVBoxLayout(config_group)
        
        # 消息内容
        config_layout.addWidget(QLabel("消息内容:"))
        self.message_edit = QTextEdit()
        self.message_edit.setPlainText("你好！")
        self.message_edit.setMaximumHeight(80)
        config_layout.addWidget(self.message_edit)
        
        # 延时设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("页面等待时间:"))
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 30)
        self.timeout_spin.setValue(5)
        self.timeout_spin.setSuffix(" 秒")
        delay_layout.addWidget(self.timeout_spin)
        config_layout.addLayout(delay_layout)
        
        # 发送间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("发送间隔:"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 10)
        self.interval_spin.setValue(1)
        self.interval_spin.setSuffix(" 秒")
        interval_layout.addWidget(self.interval_spin)
        config_layout.addLayout(interval_layout)
        
        layout.addWidget(config_group)
        
        # SEC_UID输入区域
        uid_group = QGroupBox("👥 目标用户列表")
        uid_layout = QVBoxLayout(uid_group)
        
        uid_layout.addWidget(QLabel("SEC_UID列表 (每行一个):"))
        self.uid_edit = QTextEdit()
        self.uid_edit.setPlainText("""MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x
MS4wLjABAAAAGvL6Bj9amwUVgUSwj7gBeV8Ylw8w-lFi1eqtA23MqopcQKjUHkCPsbAzFKvZKw62
MS4wLjABAAAAZC6T_MEEHdjauTZIrgj5I7hthsOS8R-9yjvntxbDJJE""")
        self.uid_edit.setMinimumHeight(200)
        uid_layout.addWidget(self.uid_edit)
        
        # 用户数量显示
        self.user_count_label = QLabel("用户数量: 0")
        uid_layout.addWidget(self.user_count_label)
        
        layout.addWidget(uid_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🚀 开始处理")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_processing)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 进度显示
        progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("准备就绪")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 统计信息
        stats_group = QGroupBox("📈 统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.total_label = QLabel("总数: 0")
        self.success_label = QLabel("成功: 0")
        self.failed_label = QLabel("失败: 0")
        
        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.success_label)
        stats_layout.addWidget(self.failed_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        # 连接信号
        self.uid_edit.textChanged.connect(self.update_user_count)
        self.update_user_count()
        
        return panel
        
    def create_log_panel(self):
        """创建右侧日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 日志标题
        log_title = QLabel("📝 处理日志")
        log_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        layout.addWidget(log_title)
        
        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_display)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        
        clear_btn = QPushButton("🗑️ 清空日志")
        clear_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_btn)
        
        save_btn = QPushButton("💾 保存日志")
        save_btn.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_btn)
        
        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)
        
        return panel
        
    def setup_style(self):
        """设置界面样式"""
        # 应用暗色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
        """)
        
        # 扩展自定义样式
        self.setStyleSheet(self.styleSheet() + """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #353535;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #ffffff;
            }
            QPushButton {
                border: 2px solid #555;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                background-color: #404040;
                color: #ffffff;
            }
            QPushButton:hover {
                border-color: #777;
                background-color: #4a4a4a;
            }
            QPushButton:pressed {
                background-color: #333;
            }
            QPushButton:disabled {
                background-color: #2a2a2a;
                color: #666;
                border-color: #333;
            }
            QTextEdit {
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
                background-color: #404040;
                color: #ffffff;
            }
            QSpinBox {
                border: 1px solid #555;
                border-radius: 4px;
                padding: 3px;
                background-color: #404040;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
            QProgressBar {
                border: 1px solid #555;
                border-radius: 4px;
                background-color: #404040;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
        """)
        
    def update_user_count(self):
        """更新用户数量显示"""
        text = self.uid_edit.toPlainText().strip()
        if text:
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            count = len(lines)
        else:
            count = 0
        self.user_count_label.setText(f"用户数量: {count}")
        
    def start_processing(self):
        """开始处理"""
        # 获取配置
        message = self.message_edit.toPlainText().strip()
        if not message:
            self.add_log("❌ 错误: 请输入消息内容")
            return
            
        uid_text = self.uid_edit.toPlainText().strip()
        if not uid_text:
            self.add_log("❌ 错误: 请输入SEC_UID列表")
            return
            
        sec_uids = [line.strip() for line in uid_text.split('\n') if line.strip()]
        if not sec_uids:
            self.add_log("❌ 错误: SEC_UID列表为空")
            return
            
        # 准备参数
        time_out = self.timeout_spin.value()
        send_interval = self.interval_spin.value()
        message_with_newline = message + '\n'
        
        # 更新界面状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("正在启动...")
        
        # 更新统计
        self.total_label.setText(f"总数: {len(sec_uids)}")
        self.success_label.setText("成功: 0")
        self.failed_label.setText("失败: 0")
        
        self.add_log(f"🚀 开始批量处理 {len(sec_uids)} 个用户")
        self.add_log(f"📝 消息内容: {message}")
        
        # 启动工作线程
        self.worker_thread = WorkerThread(sec_uids, message_with_newline, time_out, send_interval)
        self.worker_thread.progress_updated.connect(self.on_progress_updated)
        self.worker_thread.result_ready.connect(self.on_result_ready)
        self.worker_thread.log_message.connect(self.add_log)
        self.worker_thread.start()
        
    def stop_processing(self):
        """停止处理"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.worker_thread.wait(3000)  # 等待3秒
            if self.worker_thread.isRunning():
                self.worker_thread.terminate()
                self.worker_thread.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_label.setText("已停止")
        self.add_log("⏹️ 处理已停止")

    def on_progress_updated(self, percentage, current_uid):
        """处理进度更新"""
        self.progress_bar.setValue(percentage)
        if current_uid:
            self.progress_label.setText(f"正在处理: {current_uid[:20]}...")
        else:
            self.progress_label.setText("处理中...")

    def on_result_ready(self, success_count, failed_count):
        """处理完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(100)
        self.progress_label.setText("处理完成")

        total_count = success_count + failed_count

        self.success_label.setText(f"成功: {success_count}")
        self.failed_label.setText(f"失败: {failed_count}")

        self.add_log(f"✅ 批量处理完成!")
        self.add_log(f"📊 总计: {total_count} 个用户")
        self.add_log(f"✅ 成功: {success_count} 个")
        self.add_log(f"❌ 失败: {failed_count} 个")
            
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_display.append(f"[{timestamp}] {message}")
        
    def clear_log(self):
        """清空日志"""
        self.log_display.clear()
        
    def save_log(self):
        """保存日志"""
        from PyQt6.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "douyin_log.txt", "文本文件 (*.txt)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_display.toPlainText())
                self.add_log(f"💾 日志已保存到: {filename}")
            except Exception as e:
                self.add_log(f"❌ 保存失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("抖音批量私信工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = DouYinBatchUI()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
