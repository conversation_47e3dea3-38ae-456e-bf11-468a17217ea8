"""
抖音批量私信工具 - 依赖安装脚本
"""

import subprocess
import sys
import os


def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False


def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False


def main():
    """主安装函数"""
    print("🎯 抖音批量私信工具 - 依赖安装")
    print("=" * 40)
    
    # 基础依赖
    basic_packages = [
        ("DrissionPage", "DrissionPage"),
    ]
    
    # GUI依赖
    gui_packages = [
        ("PyQt6", "PyQt6"),
        ("qdarktheme", "qdarktheme"),
    ]
    
    print("📦 检查基础依赖...")
    for display_name, package_name in basic_packages:
        if check_package(package_name.split(">=")[0]):
            print(f"✅ {display_name} 已安装")
        else:
            print(f"⏳ 正在安装 {display_name}...")
            if install_package(package_name):
                print(f"✅ {display_name} 安装成功")
            else:
                print(f"❌ {display_name} 安装失败")
                return False
    
    print("\n🎨 检查GUI依赖...")
    gui_choice = input("是否安装GUI依赖？(y/n) [默认: y]: ").strip().lower()
    
    if gui_choice != 'n':
        for display_name, package_name in gui_packages:
            if check_package(package_name.split(">=")[0]):
                print(f"✅ {display_name} 已安装")
            else:
                print(f"⏳ 正在安装 {display_name}...")
                if install_package(package_name):
                    print(f"✅ {display_name} 安装成功")
                else:
                    print(f"❌ {display_name} 安装失败")
                    print("💡 提示: 可以只使用命令行版本")
    
    print("\n🎉 安装完成！")
    print("\n📖 使用说明:")
    print("- 启动程序: python run.py")
    print("- GUI版本: python ui_main.py")
    print("- 命令行版本: python main.py")
    print("- 查看帮助: python run.py --help")
    
    return True


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        print("💡 请尝试手动安装: pip install -r requirements.txt")
